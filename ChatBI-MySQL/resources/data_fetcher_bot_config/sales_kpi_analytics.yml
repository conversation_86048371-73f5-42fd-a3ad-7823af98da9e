agent_name: sales_kpi_analytics
model: google/gemini-2.5-pro
# model: moonshotai/kimi-k2
model_settings:
  temperature: 0.1
  extra_body: {"provider": {"sort": "throughput"}}
need_system_prompt: true
datasource_name: logical_dw
tools:
  - name: fetch_mysql_sql_result
  - name: get_sales_manager_team_members
  - name: fetch_ddl_for_table
agent_description: sales_kpi_analytics.md
agent_tables:
  - name: admin
    desc: 大客户表(admin_type=0)，记录大客户ID(admin_id)、大客户名字(name_remakes)、大客户所属的销售员ID(saler_id)等
  - name: crm_bd_org
    desc: 销售组织架构表，记录销售人员的基本信息，包括销售人员ID(bd_id)、销售人员名字(bd_name)、销售人员所属的上级主管名字(parent_name)等
  - name: follow_up_relation
    desc: 商户销售私海关系表(reassign=0表示私海客户)，记录商户ID(m_id)、商户所属的销售员ID(admin_id)、商户所属的销售员名字(admin_name)等
  - name: follow_up_record
    desc: 记录商户被拜访的记录(有时也叫打卡记录），包括商户ID(m_id)、拜访人ID(admin_id)、拜访人名字(admin_name)、商户所属的运营服务区编码(area_no)等
  - name: bd_mtd_comm
    desc: BD每个月的月度绩效表，包括当月的总GMV完成额、拉新完成额、利润积分、当月高价值客户数佣金、当月高价值客户数、品类推广总佣金、佣金总额、BD的ID(last_bd_id)、BD的名字(last_bd_name)等
agent_as_tool_description: |
  这是一个专门用于销售KPI分析的AI机器人，用来查询和分析销售人员的业绩数据。

  ## 和sales_order_analytics的区别

  1. sales_order_analytics机器人关注的是实时数据，而sales_kpi_analytics机器人关注的是历史数据，且是经过BI审计后的ETL处理的数据。
  2. sales_order_analytics机器人关注的是销售订单层面的分析，而sales_kpi_analytics机器人关注的是销售人员的业绩数据。

  ## 销售人员业绩定义

  已经经过BI审计完成后，归属给BD销售人员的客户下单GMV。客户下单的GMV会归属给当时拜访的BD，而不一定是当前的BD。这是非常大的区别。