CREATE TABLE `xianmu_offline_db`.`bd_mtd_comm` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增标识符',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，数据首次插入时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，数据最后修改时间',

  -- BD基础信息字段
  `bd_id` bigint NOT NULL COMMENT 'BD员工ID，关联员工表的唯一标识，用于标识具体的BD人员，如：1000768',
  `bd_name` varchar(50) NOT NULL COMMENT 'BD员工姓名，当前归属的BD销售人员姓名，如：李韬、张三、王五',
  `department_level3` varchar(50) DEFAULT NULL COMMENT '三级部门名称（大区），BD所属的大区组织架构，如：华中大区、华东大区、华南大区',
  `department_name` varchar(50) DEFAULT NULL COMMENT '部门名称（区域），BD所属的具体区域部门，如：武汉、上海、深圳',

  -- 绩效积分相关字段
  `profit_score_total` decimal(15,6) DEFAULT 0.000000 COMMENT '利润积分总数，BD当月累计获得的利润积分，用于绩效考核，支持高精度计算，如：2489.944917',
  `performance_coefficient` decimal(8,4) DEFAULT 1.0000 COMMENT '绩效系数，BD的利润积分计算系数，影响最终佣金计算，通常为1，如：1.0000',

  -- 佣金相关字段
  `commission_total_amt` decimal(12,2) DEFAULT 0.00 COMMENT '佣金总金额，BD当月获得的所有佣金总和，单位：元，如：1973.91',
  `high_value_customer_commission_amt` decimal(12,2) DEFAULT 0.00 COMMENT '高价值客户总佣金，来自A类高价值客户的佣金收入，单位：元，如：1152.50',
  `high_value_customer_count` int DEFAULT 0 COMMENT '高价值客户数量，BD服务的A类高价值客户总数，如：35',
  `high_value_customer_unit_commission` decimal(10,2) DEFAULT 0.00 COMMENT '高价值客户单客佣金，平均每个高价值客户贡献的佣金，单位：元，如：875.00',
  `excess_spu_count` bigint DEFAULT 0 COMMENT '超额SPU数量，高价值客户超出基础要求的SPU商品数量，如：185',
  `excess_spu_commission_amt` decimal(12,2) DEFAULT 0.00 COMMENT '超额SPU佣金，因超额SPU获得的额外佣金收入，单位：元，如：277.50',
  `excess_spu_customer_count` bigint DEFAULT 0 COMMENT '超额SPU客户数，产生超额SPU的高价值客户数量，如：31',

  -- 品类推广相关字段
  `category_promotion_commission_amt` decimal(12,2) DEFAULT 0.00 COMMENT '品类推广佣金总额，通过品类推广活动获得的佣金，单位：元，如：821.41',
  `existing_customer_category_commission` decimal(12,2) DEFAULT 0.00 COMMENT '存量客户品类佣金，来自现有客户的品类推广佣金，单位：元，如：589.02',
  `new_customer_category_commission` decimal(12,2) DEFAULT 0.00 COMMENT '新增客户品类佣金，来自新开发客户的品类推广佣金，单位：元，如：232.39',
  `large_sku_promotion_count` decimal(15,6) DEFAULT 0.000000 COMMENT '大规格商品推广件数，品类推广中大规格商品的销售件数，支持高精度，如：635.554610',
  `existing_customer_large_sku_count` decimal(15,6) DEFAULT 0.000000 COMMENT '存量客户大规格推广件数，现有客户购买的大规格商品件数，如：547.070023',
  `new_customer_large_sku_count` decimal(15,6) DEFAULT 0.000000 COMMENT '新增客户大规格推广件数，新客户购买的大规格商品件数，如：88.484587',

  -- 履约业绩相关字段
  `mtd_delivered_gmv_amt` decimal(15,2) DEFAULT 0.00 COMMENT 'MTD履约实付GMV，月度至今已履约订单的实际支付金额总和，单位：元，如：193732.76',
  `mtd_item_profit_amt` decimal(15,2) DEFAULT 0.00 COMMENT 'MTD履约商品毛利润，月度至今已履约商品的毛利润总额，单位：元，如：30810.09',
  `mtd_delivered_spu_count` bigint DEFAULT 0 COMMENT 'MTD履约SPU数量，月度至今已履约的不同SPU商品种类数，如：360',

  -- 数据统计字段
  `data_date` varchar(8) NOT NULL COMMENT '数据统计日期，格式YYYYMMDD，标识数据所属的统计日期，如：20250716',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bd_mtd_comm_bd_date` (`bd_id`, `data_date`) COMMENT '确保同一BD在同一天只有一条记录',
  KEY `idx_bd_mtd_comm_bd_id_commission` (`bd_id`, `commission_total_amt`) COMMENT '按BD查询佣金排序的复合索引',
  KEY `idx_bd_mtd_comm_bd_id_customer_count` (`bd_id`, `high_value_customer_count`) COMMENT '按BD查询客户数量的复合索引',
  KEY `idx_bd_mtd_comm_bd_id_gmv` (`bd_id`, `mtd_delivered_gmv_amt`) COMMENT '按BD查询GMV业绩的复合索引',
  KEY `idx_bd_mtd_comm_date_performance` (`data_date`, `profit_score_total`) COMMENT '按日期查询绩效排序的复合索引',
  KEY `idx_bd_mtd_comm_department` (`department_level3`, `department_name`) COMMENT '按部门层级查询的复合索引'
) ENGINE=InnoDB AUTO_INCREMENT=11024 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
COMMENT='BD月度至今绩效汇总表，记录每个BD销售人员的月度累计业绩数据，包括客户数量、佣金收入、品类推广、履约业绩等关键指标，用于BD绩效考核、佣金计算和业务分析。每个BD每天一条记录，支持按时间维度追踪业绩变化趋势';